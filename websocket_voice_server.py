#!/usr/bin/env python3
"""
WebSocket实时语音交互服务器
支持连续对话，无需文件上传
"""

import asyncio
import websockets
import json
import logging
import numpy as np
import base64
from pathlib import Path

logger = logging.getLogger(__name__)

class VoiceWebSocketServer:
    def __init__(self):
        self.model_manager = None
        self.clients = set()
        self.last_wake_times = {}  # 每个客户端的唤醒时间
        
    async def initialize_models(self):
        """异步初始化模型"""
        try:
            from core.model_manager import model_manager
            from utils.gpu_manager import select_device
            from configs import config
            
            device = select_device()
            config.DEVICE = device
            
            model_manager.initialize(models_to_load=['vad', 'kws', 'asr'])
            self.model_manager = model_manager
            logger.info("✅ 模型初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            return False
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        self.clients.add(websocket)
        self.last_wake_times[client_id] = 0
        
        logger.info(f"🔗 客户端连接: {client_id}")
        
        try:
            await websocket.send(json.dumps({
                "type": "status",
                "message": "🎤 语音服务已连接，可以开始说话"
            }))
            
            async for message in websocket:
                await self.process_message(websocket, client_id, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 客户端断开: {client_id}")
        except Exception as e:
            logger.error(f"❌ 处理客户端错误: {e}")
        finally:
            self.clients.discard(websocket)
            if client_id in self.last_wake_times:
                del self.last_wake_times[client_id]
    
    async def process_message(self, websocket, client_id, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            
            if msg_type == "audio":
                await self.process_audio_data(websocket, client_id, data)
            elif msg_type == "ping":
                await websocket.send(json.dumps({"type": "pong"}))
                
        except Exception as e:
            logger.error(f"❌ 处理消息失败: {e}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"处理失败: {str(e)}"
            }))
    
    async def process_audio_data(self, websocket, client_id, data):
        """处理音频数据"""
        try:
            # 解码音频数据
            audio_b64 = data.get("audio")
            sample_rate = data.get("sample_rate", 16000)
            
            audio_bytes = base64.b64decode(audio_b64)
            audio_array = np.frombuffer(audio_bytes, dtype=np.float32)
            
            # KWS检测
            kws_result = self.model_manager.kws_detect(audio_array, sample_rate)
            current_time = asyncio.get_event_loop().time()
            
            if kws_result.get("detected", False):
                if current_time - self.last_wake_times[client_id] > 2.0:
                    self.last_wake_times[client_id] = current_time
                    
                    await websocket.send(json.dumps({
                        "type": "wake_detected",
                        "keyword": kws_result.get("keyword", ""),
                        "confidence": kws_result.get("confidence", 0),
                        "message": "🔔 检测到唤醒词，我在听..."
                    }))
                    return
            
            # ASR识别
            if current_time - self.last_wake_times[client_id] <= 30.0:
                asr_result = self.model_manager.asr_recognize(audio_array, sample_rate)
                text = asr_result.get("text", "").strip()
                
                if text and len(text) > 2:
                    await websocket.send(json.dumps({
                        "type": "speech_recognized",
                        "text": text,
                        "confidence": asr_result.get("confidence", 0),
                        "message": f"🎯 识别: {text}"
                    }))
                    
                    # 这里可以接入LLM生成回复
                    # response = await self.generate_response(text)
                    # await websocket.send(json.dumps({
                    #     "type": "ai_response", 
                    #     "text": response
                    # }))
            
        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
    
    async def start_server(self, host="0.0.0.0", port=8765):
        """启动WebSocket服务器"""
        logger.info("🔄 初始化模型...")
        if not await self.initialize_models():
            logger.error("❌ 模型初始化失败")
            return
        
        logger.info(f"🚀 启动WebSocket服务器: ws://{host}:{port}")
        
        async with websockets.serve(self.handle_client, host, port):
            await asyncio.Future()  # 永远运行

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    server = VoiceWebSocketServer()
    asyncio.run(server.start_server())