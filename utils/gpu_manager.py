#!/usr/bin/env python3
"""
GPU管理器 - 智能选择最佳GPU
"""

import os
import logging
import subprocess
from typing import Optional, List, Dict

logger = logging.getLogger(__name__)

def get_gpu_info() -> List[Dict]:
    """获取GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        gpus = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 5:
                    gpus.append({
                        'index': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'utilization': int(parts[4])
                    })
        return gpus
    except Exception as e:
        logger.warning(f"无法获取GPU信息: {e}")
        return []

def select_best_gpu() -> Optional[int]:
    """选择最佳GPU"""
    gpus = get_gpu_info()
    if not gpus:
        return None
    
    # 计算每个GPU的得分 (内存使用率越低越好，利用率越低越好)
    best_gpu = None
    best_score = float('inf')
    
    for gpu in gpus:
        memory_usage_rate = gpu['memory_used'] / gpu['memory_total']
        utilization_rate = gpu['utilization'] / 100.0
        
        # 综合得分：内存使用率 + 利用率
        score = memory_usage_rate * 0.7 + utilization_rate * 0.3
        
        if score < best_score:
            best_score = score
            best_gpu = gpu['index']
    
    return best_gpu

def select_device() -> str:
    """选择计算设备"""
    # 检查环境变量
    device_env = os.environ.get("DEVICE", "").lower()
    if device_env == "cpu":
        logger.info("🖥️  环境变量指定使用CPU")
        return "cpu"
    elif device_env.startswith("cuda:"):
        logger.info(f"🖥️  环境变量指定使用: {device_env}")
        return device_env
    
    # 自动选择GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"🔍 检测到 {gpu_count} 张GPU")
            
            best_gpu = select_best_gpu()
            if best_gpu is not None:
                device = f"cuda:{best_gpu}"
                logger.info(f"🎯 自动选择GPU {best_gpu}")
                return device
            else:
                logger.info("🎯 使用默认GPU 0")
                return "cuda:0"
        else:
            logger.info("🖥️  CUDA不可用，使用CPU")
            return "cpu"
    except ImportError:
        logger.info("🖥️  PyTorch未安装，使用CPU")
        return "cpu"

def print_gpu_status():
    """打印GPU状态"""
    gpus = get_gpu_info()
    if not gpus:
        logger.info("🖥️  未检测到GPU或nvidia-smi不可用")
        return
    
    logger.info("🖥️  GPU状态:")
    for gpu in gpus:
        memory_usage = gpu['memory_used'] / gpu['memory_total'] * 100
        logger.info(f"   GPU {gpu['index']}: {gpu['name']}")
        logger.info(f"     内存: {gpu['memory_used']}MB / {gpu['memory_total']}MB ({memory_usage:.1f}%)")
        logger.info(f"     利用率: {gpu['utilization']}%")
