# 🚀 部署说明

## 环境要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **GPU**: NVIDIA GPU (可选，推荐4GB显存以上)
- **存储**: 20GB可用空间

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+推荐)
- **Python**: 3.10
- **CUDA**: 11.8+ (如使用GPU)
- **Conda**: Miniconda或Anaconda

## 安装步骤

### 1. 环境准备
```bash
# 创建Conda环境
conda create -n suziren python=3.10 -y
conda activate suziren

# 安装基础依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install modelscope funasr gradio websockets sounddevice numpy soundfile
```

### 2. 模型下载
```bash
# 创建模型目录
mkdir -p /pic/suziren/models

# 下载KWS模型
git clone https://www.modelscope.cn/iic/speech_charctc_kws_phone-xiaoyun.git /pic/suziren/models/kws/speech_charctc_kws_phone-xiaoyun

# 下载ASR模型
git clone https://www.modelscope.cn/iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git /pic/suziren/models/asr/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch

# 下载VAD模型
git clone https://www.modelscope.cn/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch.git /pic/suziren/models/vad/speech_fsmn_vad_zh-cn-16k-common-pytorch
```

### 3. 项目部署
```bash
# 克隆项目
git clone <项目地址> suziren
cd suziren

# 配置权限
chmod +x *.py
```

## 启动服务

### 方式1: Gradio界面
```bash
conda activate suziren
python main.py
```
访问: `https://localhost:7860`

### 方式2: WebSocket服务
```bash
# 启动服务端
conda activate suziren
python websocket_voice_server.py

# 启动客户端 (另一个终端)
conda activate suziren
python voice_client.py
```

## 配置说明

### 修改服务器地址
编辑 `configs.py`:
```python
SERVER_HOST = "你的服务器IP"
SERVER_PORT = 8000
```

### 修改模型路径
编辑 `configs.py`:
```python
MODEL_ROOT = "/你的模型路径"
```

### GPU配置
```python
# 自动选择GPU
DEVICE = "auto"  # 系统自动选择

# 指定GPU
DEVICE = "cuda:0"  # 使用第一块GPU

# 强制CPU
DEVICE = "cpu"  # 使用CPU
```

## 故障排除

### 1. 模型加载失败
```bash
# 检查模型路径
ls -la /pic/suziren/models/

# 检查GPU状态
nvidia-smi

# 查看错误日志
python main.py 2>&1 | tee error.log
```

### 2. WebSocket连接失败
```bash
# 检查端口占用
netstat -tulpn | grep 8765

# 检查防火墙
sudo ufw status

# 测试连接
telnet ************** 8765
```

### 3. 音频设备问题
```bash
# 检查音频设备
python -c "import sounddevice; print(sounddevice.query_devices())"

# 安装音频依赖
sudo apt-get install portaudio19-dev python3-pyaudio
```

## 性能优化

### 1. GPU优化
```python
# 限制GPU显存使用
CUDA_MEMORY_FRACTION = 0.7

# 启用混合精度
torch.backends.cudnn.benchmark = True
```

### 2. 并发优化
```python
# 调整最大连接数
MAX_CONCURRENT_SESSIONS = 10

# 调整音频缓冲区
AUDIO_CHUNK_SIZE = 1024
```

### 3. 系统优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化网络参数
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
```