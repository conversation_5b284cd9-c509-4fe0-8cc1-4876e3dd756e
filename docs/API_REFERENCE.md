# 🔌 API接口文档

## WebSocket接口

### 连接地址
```
ws://**************:8765
```

### 消息格式

#### 1. 客户端发送音频
```json
{
  "type": "audio",
  "audio": "base64编码的音频数据",
  "sample_rate": 16000,
  "timestamp": 1703123456.789
}
```

#### 2. 服务端响应 - 连接状态
```json
{
  "type": "status",
  "message": "🎧 语音服务已连接，可以开始说话"
}
```

#### 3. 服务端响应 - 唤醒检测
```json
{
  "type": "wake_detected",
  "keyword": "小云",
  "confidence": 0.85,
  "message": "🔔 检测到唤醒词: 小云"
}
```

#### 4. 服务端响应 - 语音识别
```json
{
  "type": "speech_recognized",
  "text": "今天天气怎么样",
  "confidence": 0.92,
  "message": "🎯 识别: 今天天气怎么样"
}
```

#### 5. 服务端响应 - 错误信息
```json
{
  "type": "error",
  "message": "音频处理失败: 模型未加载"
}
```

### 心跳机制
```json
// 客户端发送
{"type": "ping"}

// 服务端响应
{"type": "pong"}
```

## Gradio界面API

### 启动参数
```python
interface.launch(
    server_name="0.0.0.0",
    server_port=7860,
    ssl_verify=False,
    share=False
)
```

### 主要组件

#### 1. 开始监听
- **输入**: 无
- **输出**: 状态信息, 界面更新
- **功能**: 激活音频监听模式

#### 2. 音频处理
- **输入**: (sample_rate, audio_array)
- **输出**: 处理结果, 状态更新, 历史记录
- **功能**: KWS检测和ASR识别

#### 3. 停止监听
- **输入**: 无
- **输出**: 状态信息, 界面更新
- **功能**: 停止音频监听

## 配置参数

### 模型配置
```python
MODEL_ROOT = "/pic/suziren/models"
KWS_MODEL = "kws/speech_charctc_kws_phone-xiaoyun"
ASR_MODEL = "asr/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
VAD_MODEL = "vad/speech_fsmn_vad_zh-cn-16k-common-pytorch"
```

### 音频配置
```python
AUDIO_SAMPLE_RATE = 16000
AUDIO_CHANNELS = 1
AUDIO_FORMAT = "int16"
```

### 服务配置
```python
SERVER_HOST = "**************"
SERVER_PORT = 8000
MAX_CONCURRENT_SESSIONS = 10
```

### KWS配置
```python
KWS_KEYWORDS = ["小云", "你好小云", "嗨小云"]
KWS_THRESHOLD = 0.5
```