# 🏗️ 系统架构设计

## 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端层      │    │    服务端层     │    │   模型层        │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Web界面       │    │ • WebSocket服务 │    │ • KWS唤醒模型   │
│ • 实时录音      │◄──►│ • 音频流处理    │◄──►│ • ASR识别模型   │
│ • 状态显示      │    │ • 会话管理      │    │ • VAD检测模型   │
│ • 命令行客户端  │    │ • 并发控制      │    │ • 降噪模型      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心模块

### 1. 模型管理器 (ModelManager)
- **功能**: 统一管理AI模型加载、初始化和推理
- **支持模型**: KWS、ASR、VAD、降噪
- **特性**: GPU自动选择、CPU回退、内存优化

### 2. 音频处理管道 (AudioPipeline)
- **功能**: 实时音频流处理和状态管理
- **流程**: 音频采集 → 预处理 → 模型推理 → 结果输出
- **状态**: listening → wake_detected → recognizing

### 3. WebSocket服务 (WebSocketService)
- **功能**: 实时双向通信和多客户端管理
- **特性**: 心跳保活、断线重连、并发处理
- **协议**: JSON格式消息传输

### 4. GPU管理器 (GPUManager)
- **功能**: 智能设备选择和资源监控
- **特性**: 显存检测、负载均衡、故障转移

## 数据流

1. **音频采集**: 客户端实时录音 → Base64编码 → WebSocket传输
2. **唤醒检测**: 音频解码 → KWS模型推理 → 唤醒状态更新
3. **语音识别**: 唤醒后音频 → ASR模型推理 → 文本输出
4. **结果反馈**: 处理结果 → JSON封装 → 客户端显示

## 技术栈

- **后端**: Python 3.10, WebSockets, AsyncIO
- **AI框架**: ModelScope, FunASR, PyTorch
- **前端**: Gradio, HTML5 Audio API
- **部署**: Conda环境, CUDA支持