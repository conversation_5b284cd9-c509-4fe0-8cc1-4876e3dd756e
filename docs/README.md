# 🎤 全息屏数字人语音交互系统

基于正确的KWS实现重新构建的语音交互系统。

## 🏗️ 项目结构

```
suziren/
├── core/                    # 核心模块
│   ├── model_manager.py     # 模型管理器 (基于正确的KWS实现)
│   └── audio_pipeline.py    # 音频处理管道
├── services/                # 服务层
│   └── websocket_service.py # WebSocket服务
├── client/                  # 客户端
│   └── realtime_client.py   # 实时音频客户端
├── utils/                   # 工具模块
│   └── gpu_manager.py       # GPU管理器
├── docs/                    # 文档
│   └── README.md
├── configs.py               # 配置管理
├── main.py                  # 主入口
└── 1.py                     # 正确的KWS实现参考
```

## ✨ 核心特性

### 🤖 AI模型集成
- **KWS (唤醒词检测)**: 使用ModelScope pipeline方式 ✅
- **VAD (语音活动检测)**: FunASR AutoModel
- **ASR (语音识别)**: FunASR AutoModel  
- **PUNC (标点恢复)**: FunASR AutoModel

### 🎵 音频处理
- 实时音频流处理
- 智能状态管理 (listening → wake_detected → recognizing)
- 音频缓冲和超时处理

### 🌐 网络服务
- WebSocket实时通信
- FastAPI集成
- 多会话并发支持

### 🖥️ 智能设备管理
- 自动GPU选择
- CPU回退机制
- 显存监控

## 🚀 快速启动

### 服务器端
```bash
# 1. 激活环境
conda activate suziren

# 2. 启动服务
python main.py
```

### 客户端
```bash
# 1. 激活环境
conda activate llm

# 2. 安装依赖
pip install sounddevice websockets numpy

# 3. 启动客户端
python client/realtime_client.py
```

## 🔧 配置说明

### 模型路径配置 (configs.py)
```python
MODEL_ROOT = "/pic/suziren/models"
KWS_MODEL = os.path.join(MODEL_ROOT, "kws/speech_charctc_kws_phone-xiaoyun")
VAD_MODEL = os.path.join(MODEL_ROOT, "vad/speech_fsmn_vad_zh-cn-16k-common-pytorch")
ASR_MODEL = os.path.join(MODEL_ROOT, "asr/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch")
PUNC_MODEL = os.path.join(MODEL_ROOT, "punc/punc_ct-transformer_zh-cn-common-vocab272727-pytorch")
```

### KWS配置
```python
KWS_KEYWORDS = ["小云", "你好小云", "嗨小云"]
KWS_THRESHOLD = 0.5
```

## 🎯 使用流程

1. **启动服务器**: `python main.py`
2. **连接客户端**: 运行 `python client/realtime_client.py`
3. **开始录音**: 点击"开始录音"按钮
4. **语音交互**: 说出"小云"唤醒，然后进行对话

## 🔍 关键改进

### 正确的KWS实现
基于您的 `1.py` 文件，使用ModelScope pipeline方式：

```python
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks

model = pipeline(
    task=Tasks.keyword_spotting,
    model=config.KWS_MODEL  # 本地路径
)

result = model(audio_data)
```

### 智能状态管理
- `listening`: 监听唤醒词
- `wake_detected`: 检测到唤醒词
- `recognizing`: 进行语音识别

### 完整的错误处理
- 模型加载失败处理
- 网络连接异常处理
- 音频设备错误处理

## 📊 系统要求

### 服务器端
- Python 3.9+
- CUDA支持的GPU (可选)
- ModelScope
- FunASR

### 客户端
- Python 3.9+
- sounddevice
- websockets
- tkinter (通常内置)

## 🎉 项目亮点

1. **基于正确实现**: 使用您验证过的KWS实现方式
2. **模块化设计**: 清晰的代码结构
3. **智能设备管理**: 自动选择最佳GPU
4. **实时交互**: 真正的语音对话体验
5. **完整的错误处理**: 各种异常情况的处理

---

**基于正确的KWS实现重新构建** ✅  
**状态**: 可用于测试  
**下一步**: 在服务器上测试KWS模型加载
