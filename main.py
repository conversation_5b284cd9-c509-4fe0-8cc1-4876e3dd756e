#!/usr/bin/env python3
"""
语音交互系统 - 完整版本
"""

import gradio as gr
import numpy as np
import logging
import time
import os
import sys
import threading
import json
from pathlib import Path
from typing import Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# 设置自定义临时目录
TEMP_DIR = "/pic/suziren/gradio"
Path(TEMP_DIR).mkdir(parents=True, exist_ok=True)
os.environ["GRADIO_TEMP_DIR"] = TEMP_DIR

# 音频流处理器 (为下周WebSocket做准备)
class AudioStreamProcessor:
    """音频流处理器 - 为流式传输做准备"""

    def __init__(self, voice_app):
        self.voice_app = voice_app
        self.audio_chunks = []

    def save_audio_chunk(self, audio_array, sample_rate):
        """保存音频片段 (模拟流式传输的分段保存)"""
        try:
            timestamp = int(time.time() * 1000)
            audio_file = f"{TEMP_DIR}/audio_chunk_{timestamp}.wav"

            import soundfile as sf
            sf.write(audio_file, audio_array, sample_rate)

            self.audio_chunks.append({
                "timestamp": timestamp,
                "file": audio_file,
                "duration": len(audio_array) / sample_rate
            })

            logger.info(f"💾 保存音频片段: {audio_file} ({len(audio_array)/sample_rate:.1f}秒)")

            # 只保留最近10个片段
            if len(self.audio_chunks) > 10:
                old_chunk = self.audio_chunks.pop(0)
                try:
                    os.remove(old_chunk["file"])
                except:
                    pass

            return audio_file

        except Exception as e:
            logger.error(f"保存音频片段失败: {e}")
            return None

class VoiceInteractionApp:
    """语音交互应用"""

    def __init__(self):
        self.model_manager = None
        self.conversation_history = []
        self.last_wake_time = 0
        self.wake_cooldown = 2.0
        self._model_initialized = False
        self.is_listening = False
        self.current_state = "idle"  # idle, listening, wake_detected, recognizing
        self.stream_processor = AudioStreamProcessor(self)

    def initialize_models(self):
        """初始化AI模型"""
        if self._model_initialized:
            return "✅ 模型已加载"

        try:
            logger.info("🔄 开始初始化模型...")

            from core.model_manager import model_manager
            from utils.gpu_manager import select_device
            from configs import config

            # 检查设备
            device = select_device()
            config.DEVICE = device
            config.SELECTED_GPU = int(device.split(':')[1]) if 'cuda:' in device else None

            logger.info(f"🎯 使用设备: {device}")

            # 检查模型文件
            logger.info("📋 检查模型文件...")
            model_status = config.validate_models()
            for name, exists in model_status.items():
                status = "✅" if exists else "❌"
                logger.info(f"   {status} {name}")

            # 检查缺失的模型
            missing_models = [name for name, exists in model_status.items() if not exists]
            if missing_models:
                error_msg = f"❌ 缺少模型文件: {missing_models}\n请先运行: python scripts/download_models.py"
                logger.error(error_msg)
                return error_msg

            # 初始化模型
            logger.info("🚀 开始加载模型...")
            model_manager.initialize(models_to_load=['vad', 'kws', 'asr'])
            self.model_manager = model_manager
            self._model_initialized = True

            logger.info("✅ 模型初始化完成")
            return f"✅ 模型加载成功\n🎯 设备: {device}\n📁 模型路径: /pic/suziren/models/"

        except Exception as e:
            error_msg = f"❌ 模型初始化失败: {e}"
            logger.error(error_msg)
            import traceback
            traceback.print_exc()
            return error_msg

    def start_listening(self):
        """开始持续监听 - 返回更新后的组件状态"""
        if self.is_listening:
            return (
                "⚠️ 已经在监听中",
                self.get_status(),
                self.format_history(),
                gr.Audio(interactive=True, label="🎤 录音 (监听已激活)"),  # 启用录音
                gr.Button("🎯 处理音频", variant="primary", interactive=True)  # 启用按钮
            )

        if not self._model_initialized:
            init_result = self.initialize_models()
            if "失败" in init_result:
                return (
                    init_result,
                    "❌ 模型未加载",
                    self.format_history(),
                    gr.Audio(interactive=False, label="🎤 录音 (模型未加载)"),
                    gr.Button("🎯 处理音频", variant="primary", interactive=False)
                )

        self.is_listening = True
        self.current_state = "listening"

        self.add_message("系统", "🎧 持续监听已启动")
        self.add_message("提示", "💡 现在可以点击录音按钮")
        self.add_message("提示", "🎤 录音时浏览器会请求麦克风权限，请点击'允许'")
        self.add_message("提示", "🗣️ 先说'小云'唤醒，然后说出问题")

        return (
            "🎧 持续监听已启动\n💡 现在可以点击录音按钮\n🎤 浏览器会请求麦克风权限",
            "🎧 监听中",
            self.format_history(),
            gr.Audio(interactive=True, label="🎤 录音 (监听已激活，点击录音会请求麦克风权限)"),
            gr.Button("🎯 处理音频", variant="primary", interactive=True)
        )

    def stop_listening(self):
        """停止监听 - 返回更新后的组件状态"""
        self.is_listening = False
        self.current_state = "idle"

        self.add_message("系统", "🔇 停止监听")
        return (
            "🔇 已停止监听",
            "🔇 已停止",
            self.format_history(),
            gr.Audio(interactive=False, label="🎤 录音 (需要先开始监听)"),
            gr.Button("🎯 处理音频", variant="primary", interactive=False)
        )

    def process_audio(self, audio_data) -> Tuple[str, str, str]:
        """处理音频数据"""
        if audio_data is None:
            return "❌ 没有音频数据", self.get_status(), self.format_history()

        logger.info("🎵 开始处理音频...")

        if not self._model_initialized:
            logger.info("🔄 模型未初始化，开始加载...")
            init_result = self.initialize_models()
            if "失败" in init_result:
                return init_result, "❌ 模型未加载", self.format_history()

        try:
            logger.info(f"🎵 处理音频，监听状态: {self.is_listening}")

            sample_rate, audio_array = audio_data

            # 转换为单声道
            if len(audio_array.shape) > 1:
                audio_array = audio_array[:, 0]

            # 转换为float32格式
            if audio_array.dtype == np.int16:
                audio_array = audio_array.astype(np.float32) / 32768.0
            elif audio_array.dtype == np.int32:
                audio_array = audio_array.astype(np.float32) / 2147483648.0

            duration = len(audio_array) / sample_rate
            if duration < 1.0:
                return "⚠️ 音频太短，请说长一点", self.get_status(), self.format_history()

            logger.info(f"🎵 处理音频: {duration:.1f}秒")

            # 保存音频片段 (为流式传输做准备)
            self.stream_processor.save_audio_chunk(audio_array, sample_rate)

            # KWS检测
            kws_result = self.model_manager.kws_detect(audio_array, sample_rate)
            current_time = time.time()

            if kws_result.get("detected", False):
                if current_time - self.last_wake_time > self.wake_cooldown:
                    self.last_wake_time = current_time
                    self.current_state = "wake_detected"

                    keyword = kws_result.get("keyword", "")
                    confidence = kws_result.get("confidence", 0) * 100

                    self.add_message("唤醒", f"🔔 检测到唤醒词: {keyword} (置信度: {confidence:.1f}%)")
                    self.add_message("系统", "💬 我在听，请继续录音说出您的问题...")

                    msg = f"🔔 检测到唤醒词: {keyword} (置信度: {confidence:.1f}%)"
                    if self.is_listening:
                        msg += "\n💬 我在听，请继续录音说出您的问题..."
                    return msg, self.get_status(), self.format_history()
                else:
                    return "⏰ 唤醒冷却中，请稍后再试", self.get_status(), self.format_history()

            # ASR识别 (如果最近被唤醒)
            if current_time - self.last_wake_time <= 30.0:
                self.current_state = "recognizing"

                asr_result = self.model_manager.asr_recognize(audio_array, sample_rate)
                text = asr_result.get("text", "").strip()

                if text and len(text) > 2 and not self.is_noise(text):
                    confidence = asr_result.get("confidence", 0) * 100

                    self.add_message("识别", f"🎯 \"{text}\" (置信度: {confidence:.1f}%)")
                    self.add_message("系统", "💡 继续监听中，可以继续录音提问...")

                    msg = f"🎯 识别结果: \"{text}\" (置信度: {confidence:.1f}%)"
                    if self.is_listening:
                        msg += "\n💡 继续监听中，可以继续录音提问..."
                    return msg, self.get_status(), self.format_history()
                else:
                    return "🔇 请说清楚一点，或者先说唤醒词'小云'", self.get_status(), self.format_history()
            else:
                return "💤 请先说唤醒词 '小云' 来激活语音助手", self.get_status(), self.format_history()

        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
            import traceback
            traceback.print_exc()
            return f"❌ 处理失败: {e}", self.get_status(), self.format_history()

    def add_message(self, sender: str, content: str):
        """添加消息到历史记录"""
        self.conversation_history.append({
            "type": sender.lower(),
            "content": content,
            "time": time.strftime("%H:%M:%S")
        })

    def get_status(self) -> str:
        """获取当前状态"""
        status_map = {
            "idle": "🔇 空闲",
            "listening": "🎧 监听中",
            "wake_detected": "🔔 已唤醒",
            "recognizing": "🎯 识别中"
        }
        return status_map.get(self.current_state, "❓ 未知状态")

    def is_noise(self, text):
        """判断是否为噪音"""
        noise_patterns = ["啊", "呃", "嗯", "哦", "额", "呀", "哎", "唉", "阿"]
        clean_text = text.replace(" ", "").strip()
        return clean_text in noise_patterns or len(clean_text) <= 2

    def format_history(self) -> str:
        """格式化对话历史"""
        if not self.conversation_history:
            return "📝 对话历史为空\n\n💡 使用说明:\n1. 点击'开始监听'激活系统\n2. 使用'语音输入'录音\n3. 先说'小云'进行唤醒\n4. 然后说出您的问题\n5. 点击'处理音频'查看结果"

        history_text = "📝 对话历史:\n\n"
        for i, item in enumerate(self.conversation_history[-15:], 1):
            time_str = item.get("time", "")
            content = item.get("content", "")
            sender = item.get("type", "")

            if sender == "系统":
                history_text += f"[{time_str}] 🤖 {content}\n"
            elif sender == "唤醒":
                history_text += f"[{time_str}] 🔔 {content}\n"
            elif sender == "识别":
                history_text += f"[{time_str}] 🎯 {content}\n"
            elif sender == "提示":
                history_text += f"[{time_str}] 💡 {content}\n"
            else:
                history_text += f"[{time_str}] {content}\n"

        return history_text

    def force_reinitialize(self):
        """强制重新初始化模型"""
        self._model_initialized = False
        self.model_manager = None
        logger.info("🔄 强制重新初始化模型...")
        result = self.initialize_models()
        return result, self.get_status()

    def clear_history(self) -> Tuple[str, str]:
        """清空对话历史"""
        self.conversation_history.clear()
        self.last_wake_time = 0
        return "🔄 对话历史已清空", self.format_history()

    def create_interface(self):
        """创建Gradio界面"""
        # 设置文件大小限制 (兼容不同Gradio版本)
        try:
            interface = gr.Blocks(title="🎤 语音交互系统", theme=gr.themes.Soft())
        except Exception:
            interface = gr.Blocks(title="🎤 语音交互系统")

        with interface:
            gr.Markdown("""
            # 🎤 语音交互系统

            **使用流程**: 开始监听 → 录音说"小云" → 录音说问题 → 查看结果
            """)

            with gr.Row():
                # 左侧控制面板
                with gr.Column(scale=1):
                    gr.Markdown("## 🎛️ 控制面板")

                    # 监听控制
                    with gr.Group():
                        gr.Markdown("### 🎧 监听控制")

                        start_btn = gr.Button("🎧 开始监听", variant="primary", size="lg")
                        stop_btn = gr.Button("🔇 停止监听", variant="secondary", size="lg")

                        status_display = gr.Textbox(
                            label="当前状态",
                            value="🔇 空闲",
                            interactive=False
                        )

                    # 语音输入
                    with gr.Group():
                        gr.Markdown("### 🎤 语音输入")
                        gr.Markdown("**使用方法**: 上传音频文件或录音 (如果浏览器支持)")
                        gr.Markdown("**💡 提示**: 如果麦克风不可用，请录制音频文件上传")

                        audio_input = gr.Audio(
                            sources=["upload"],  # 只使用上传，避免麦克风权限问题
                            type="numpy",
                            label="📁 上传音频文件 (WAV/MP3格式)"
                        )

                        process_btn = gr.Button("🎯 处理音频", variant="primary")

                    # 系统信息
                    with gr.Group():
                        gr.Markdown("### 📊 系统信息")

                        system_status = gr.Textbox(
                            label="系统状态",
                            value="正在初始化...",
                            interactive=False,
                            lines=5
                        )

                        with gr.Row():
                            refresh_btn = gr.Button("🔄 刷新状态", variant="secondary")
                            init_btn = gr.Button("🚀 重新初始化", variant="primary")

                # 右侧对话区域
                with gr.Column(scale=2):
                    gr.Markdown("## 💬 实时对话")

                    # 处理结果显示
                    result_display = gr.Textbox(
                        label="📢 最新结果",
                        lines=4,
                        interactive=False,
                        placeholder="等待语音输入..."
                    )

                    # 对话历史
                    chat_history = gr.Textbox(
                        label="📝 对话历史",
                        lines=18,
                        interactive=False,
                        value="📝 对话历史为空\n\n💡 使用说明:\n1. 点击'开始监听'激活系统\n2. 使用'语音输入'录音\n3. 先说'小云'进行唤醒\n4. 然后说出您的问题\n5. 点击'处理音频'查看结果"
                    )

                    # 控制按钮
                    with gr.Row():
                        clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")

            # 事件绑定
            start_btn.click(
                fn=self.start_listening,
                outputs=[result_display, status_display, chat_history, audio_input, process_btn]
            )

            stop_btn.click(
                fn=self.stop_listening,
                outputs=[result_display, status_display, chat_history, audio_input, process_btn]
            )

            process_btn.click(
                fn=self.process_audio,
                inputs=[audio_input],
                outputs=[result_display, status_display, chat_history]
            )

            clear_btn.click(
                fn=self.clear_history,
                outputs=[result_display, chat_history]
            )

            refresh_btn.click(
                fn=lambda: (self.initialize_models(), self.get_status()),
                outputs=[system_status, status_display]
            )

            init_btn.click(
                fn=self.force_reinitialize,
                outputs=[system_status, status_display]
            )

            # 页面加载时初始化
            interface.load(
                fn=lambda: (self.initialize_models(), self.get_status()),
                outputs=[system_status, status_display]
            )

        return interface

def generate_ssl_cert():
    """生成自签名SSL证书"""
    import subprocess

    cert_dir = Path("ssl_certs")
    cert_dir.mkdir(exist_ok=True)

    cert_file = cert_dir / "cert.pem"
    key_file = cert_dir / "key.pem"

    if cert_file.exists() and key_file.exists():
        logger.info("✅ SSL证书已存在")
        return str(cert_file), str(key_file)

    try:
        # 生成自签名证书
        cmd = [
            "openssl", "req", "-x509", "-newkey", "rsa:2048",
            "-keyout", str(key_file),
            "-out", str(cert_file),
            "-days", "365", "-nodes",
            "-subj", "/C=CN/ST=State/L=City/O=VoiceChat/CN=**************"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ SSL证书生成成功")
            return str(cert_file), str(key_file)
        else:
            logger.error(f"❌ 证书生成失败: {result.stderr}")
            return None, None

    except FileNotFoundError:
        logger.error("❌ 未找到openssl命令")
        return None, None
    except Exception as e:
        logger.error(f"❌ 证书生成异常: {e}")
        return None, None

def find_free_port(start_port=7860):
    """查找可用端口"""
    import socket

    for port in range(start_port, start_port + 10):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def setup_ssl_environment():
    """设置SSL环境，禁用所有证书验证"""
    import ssl

    # 创建不验证证书的SSL上下文
    ssl._create_default_https_context = ssl._create_unverified_context

    # 设置环境变量
    os.environ['PYTHONHTTPSVERIFY'] = '0'
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''

    try:
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    except:
        pass

    try:
        import requests
        requests.packages.urllib3.disable_warnings()
    except:
        pass

def main():
    """主函数"""
    try:
        logger.info("🎤 语音交互系统 - HTTPS模式")
        logger.info("=" * 50)

        # 设置SSL环境
        setup_ssl_environment()
        logger.info("🔧 SSL环境配置完成")

        # 查找可用端口
        port = find_free_port(7860)
        if not port:
            logger.error("❌ 无法找到可用端口")
            sys.exit(1)

        logger.info(f"🔍 使用端口: {port}")

        # 生成SSL证书
        logger.info("🔒 准备SSL证书...")
        cert_file, key_file = generate_ssl_cert()

        if not cert_file or not key_file:
            logger.error("❌ SSL证书生成失败")
            sys.exit(1)

        # 创建应用
        app = VoiceInteractionApp()
        interface = app.create_interface()

        # 启动HTTPS模式 - 使用自定义方式
        logger.info("🔒 启动HTTPS模式")
        logger.info(f"🌐 访问地址: https://**************:{port}")
        logger.info("🎤 HTTPS模式支持麦克风访问")
        logger.info("⚠️  浏览器会显示证书警告，请点击'高级' → '继续访问'")
        logger.info("🎯 点击'开始监听'后，录音按钮会触发麦克风权限请求")

        # 使用自定义启动方式
        import uvicorn
        import ssl

        # 创建SSL上下文
        ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        ssl_context.load_cert_chain(cert_file, key_file)
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 获取Gradio的FastAPI应用
        fastapi_app = interface.app

        # 使用uvicorn启动HTTPS
        uvicorn.run(
            fastapi_app,
            host="0.0.0.0",
            port=port,
            ssl_keyfile=key_file,
            ssl_certfile=cert_file,
            ssl_version=ssl.PROTOCOL_TLS_SERVER,
            ssl_cert_reqs=ssl.CERT_NONE,
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("👋 程序被用户中断")
    except Exception as e:
        logger.error(f"❌ 程序启动失败: {e}")

        # 如果HTTPS失败，尝试HTTP
        logger.info("🔄 尝试HTTP模式作为备用...")
        try:
            port = find_free_port(7861)  # 使用不同端口
            app = VoiceInteractionApp()
            interface = app.create_interface()

            logger.info(f"🌐 HTTP访问地址: http://**************:{port}")
            logger.info("💡 HTTP模式下请使用文件上传功能测试")

            interface.launch(
                server_name="0.0.0.0",
                server_port=port,
                share=False,
                show_error=True,
                quiet=False
            )
        except Exception as backup_error:
            logger.error(f"❌ HTTP备用模式也失败: {backup_error}")
            sys.exit(1)

if __name__ == "__main__":
    main()
