#!/usr/bin/env python3
"""
WebSocket实时语音交互服务器 - 修复版
"""

import asyncio
import websockets
import json
import logging
import numpy as np
import base64
import sys
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VoiceWebSocketServer:
    def __init__(self):
        self.model_manager = None
        self.clients = set()
        self.last_wake_times = {}
        
    async def initialize_models(self):
        """异步初始化模型"""
        try:
            logger.info("🔄 开始初始化模型...")
            
            from core.model_manager import model_manager
            from utils.gpu_manager import select_device
            from configs import config
            
            device = select_device()
            config.DEVICE = device
            logger.info(f"🎯 使用设备: {device}")
            
            model_manager.initialize(models_to_load=['kws', 'asr'])
            self.model_manager = model_manager
            logger.info("✅ 模型初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            traceback.print_exc()
            return False
    
    async def handle_client(self, websocket, path):
        """处理客户端连接 - 修复参数问题"""
        try:
            client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
            self.clients.add(websocket)
            self.last_wake_times[client_id] = 0
            
            logger.info(f"🔗 客户端连接: {client_id}")
            
            # 发送连接成功消息
            await websocket.send(json.dumps({
                "type": "status",
                "message": "🎤 语音服务已连接，可以开始说话"
            }))
            
            # 处理客户端消息
            async for message in websocket:
                try:
                    await self.process_message(websocket, client_id, message)
                except Exception as e:
                    logger.error(f"❌ 处理消息失败: {e}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": f"处理失败: {str(e)}"
                    }))
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 客户端断开: {client_id}")
        except Exception as e:
            logger.error(f"❌ 客户端处理错误: {e}")
            traceback.print_exc()
        finally:
            # 清理客户端信息
            self.clients.discard(websocket)
            if 'client_id' in locals() and client_id in self.last_wake_times:
                del self.last_wake_times[client_id]
    
    async def process_message(self, websocket, client_id, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            
            if msg_type == "audio":
                await self.process_audio_data(websocket, client_id, data)
            elif msg_type == "ping":
                await websocket.send(json.dumps({"type": "pong"}))
            else:
                logger.warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
        except Exception as e:
            logger.error(f"❌ 处理消息失败: {e}")
            raise
    
    async def process_audio_data(self, websocket, client_id, data):
        """处理音频数据"""
        try:
            # 检查模型是否已加载
            if self.model_manager is None:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "模型未初始化"
                }))
                return
            
            # 解码音频数据
            audio_b64 = data.get("audio")
            sample_rate = data.get("sample_rate", 16000)
            
            if not audio_b64:
                logger.warning("收到空音频数据")
                return
            
            # 解码音频
            audio_bytes = base64.b64decode(audio_b64)
            audio_array = np.frombuffer(audio_bytes, dtype=np.float32)
            
            logger.info(f"📥 收到音频: {len(audio_array)/sample_rate:.1f}秒")
            
            # KWS检测
            kws_result = self.model_manager.kws_detect(audio_array, sample_rate)
            current_time = asyncio.get_event_loop().time()
            
            if kws_result.get("detected", False):
                # 检查唤醒冷却时间
                if current_time - self.last_wake_times[client_id] > 2.0:
                    self.last_wake_times[client_id] = current_time
                    
                    keyword = kws_result.get("keyword", "")
                    confidence = kws_result.get("confidence", 0)
                    
                    logger.info(f"🔔 检测到唤醒词: {keyword}")
                    
                    await websocket.send(json.dumps({
                        "type": "wake_detected",
                        "keyword": keyword,
                        "confidence": confidence,
                        "message": f"🔔 检测到唤醒词: {keyword}"
                    }))
                    return
            
            # ASR识别 (如果最近被唤醒)
            if current_time - self.last_wake_times[client_id] <= 30.0:
                asr_result = self.model_manager.asr_recognize(audio_array, sample_rate)
                text = asr_result.get("text", "").strip()
                
                if text and len(text) > 2:
                    confidence = asr_result.get("confidence", 0)
                    
                    logger.info(f"🎯 识别结果: {text}")
                    
                    await websocket.send(json.dumps({
                        "type": "speech_recognized",
                        "text": text,
                        "confidence": confidence,
                        "message": f"🎯 识别: {text}"
                    }))
                    
                    # TODO: 这里可以接入LLM生成回复
                    # response = await self.generate_response(text)
                    # await websocket.send(json.dumps({
                    #     "type": "ai_response", 
                    #     "text": response
                    # }))
            
        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
            traceback.print_exc()
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"音频处理失败: {str(e)}"
            }))
    
    async def start_server(self, host="0.0.0.0", port=8765):
        """启动WebSocket服务器"""
        logger.info("🔄 初始化模型...")
        if not await self.initialize_models():
            logger.error("❌ 模型初始化失败，无法启动服务器")
            return False
        
        logger.info(f"🚀 启动WebSocket服务器: ws://{host}:{port}")
        
        try:
            async with websockets.serve(self.handle_client, host, port):
                logger.info("✅ 服务器启动成功，等待客户端连接...")
                await asyncio.Future()  # 永远运行
        except Exception as e:
            logger.error(f"❌ 服务器启动失败: {e}")
            return False

async def main():
    """主函数"""
    try:
        server = VoiceWebSocketServer()
        await server.start_server()
    except KeyboardInterrupt:
        logger.info("👋 服务器被用户中断")
    except Exception as e:
        logger.error(f"❌ 服务器运行失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())