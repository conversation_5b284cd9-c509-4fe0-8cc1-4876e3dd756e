#!/usr/bin/env python3
"""
配置管理模块
"""

import os
from pathlib import Path

class Config:
    """系统配置类"""
    
    # 项目路径
    PROJECT_ROOT = Path(__file__).parent
    MODEL_ROOT = "/pic/suziren/models"
    
    # 设备配置
    DEVICE = "cuda:0"  # 默认设备，会被动态选择覆盖
    SELECTED_GPU = 0
    CUDA_MEMORY_FRACTION = 0.7
    
    # 音频配置
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_CHANNELS = 1
    AUDIO_FORMAT = "int16"
    
    # 模型路径配置
    VAD_MODEL = os.path.join(MODEL_ROOT, "vad/speech_fsmn_vad_zh-cn-16k-common-pytorch")
    ASR_MODEL = os.path.join(MODEL_ROOT, "asr/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch")
    KWS_MODEL = os.path.join(MODEL_ROOT, "kws/speech_charctc_kws_phone-xiaoyun")

    
    # KWS配置
    KWS_KEYWORDS = ["小云", "你好小云", "嗨小云"]
    KWS_THRESHOLD = 0.5
    
    # 服务器配置
    SERVER_HOST = "**************"
    SERVER_PORT = 8000
    MAX_CONCURRENT_SESSIONS = 10
    
    @classmethod
    def validate_models(cls):
        """验证模型路径"""
        models = {
            "VAD": cls.VAD_MODEL,
            "ASR": cls.ASR_MODEL,
            "KWS": cls.KWS_MODEL,
        }
        
        results = {}
        for name, path in models.items():
            results[name] = Path(path).exists()
        
        return results

# 创建全局配置实例
config = Config()
