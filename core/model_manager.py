#!/usr/bin/env python3
"""
模型管理器 - 基于正确的KWS实现
"""

import logging
import threading
from typing import Dict, Any, Optional, List
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)

class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self._models = {}
        self._model_lock = threading.Lock()
        self._initialized = False
    
    def initialize(self, models_to_load: List[str] = None):
        """初始化模型管理器"""
        if self._initialized:
            logger.info("模型管理器已初始化")
            return
        
        from configs import config
        
        # 验证模型文件
        model_status = config.validate_models()
        missing_models = [name for name, exists in model_status.items() if not exists]
        
        if missing_models:
            logger.error(f"❌ 缺少模型: {missing_models}")
            raise FileNotFoundError(f"模型文件缺失: {missing_models}")
        
        # 加载指定模型
        if models_to_load is None:
            models_to_load = ["vad", "kws", "asr"]
        
        for model_type in models_to_load:
            try:
                logger.info(f"🔄 开始加载 {model_type} 模型...")
                self._load_model(model_type)
                logger.info(f"✅ {model_type} 模型加载完成")
            except Exception as e:
                logger.error(f"❌ 加载模型 {model_type} 失败: {e}")
                import traceback
                traceback.print_exc()
        
        self._initialized = True
        logger.info("✅ 模型管理器初始化完成")
    
    def _load_model(self, model_type: str):
        """加载单个模型"""
        from configs import config
        
        model_type = model_type.lower()
        
        try:
            if model_type == "kws":
                # 使用ModelScope pipeline方式加载KWS (基于您的正确实现)
                from modelscope.pipelines import pipeline
                from modelscope.utils.constant import Tasks
                
                logger.info(f"🔧 加载KWS模型: {config.KWS_MODEL}")
                
                model = pipeline(
                    task=Tasks.keyword_spotting,
                    model=config.KWS_MODEL  # 使用本地路径
                )
                
            elif model_type == "vad":
                # VAD模型
                from funasr import AutoModel
                
                logger.info(f"🔧 加载VAD模型: {config.VAD_MODEL}")
                
                model = AutoModel(
                    model=config.VAD_MODEL,
                    device=config.DEVICE,
                    disable_update=True
                )
                
            elif model_type == "asr":
                # ASR模型 - 如果GPU有cuDNN问题，使用CPU
                from funasr import AutoModel

                logger.info(f"🔧 加载ASR模型: {config.ASR_MODEL}")

                # 尝试GPU，失败则使用CPU
                try:
                    model = AutoModel(
                        model=config.ASR_MODEL,
                        device=config.DEVICE,
                        disable_update=True
                    )
                    logger.info(f"✅ ASR模型使用GPU: {config.DEVICE}")
                except Exception as gpu_error:
                    logger.warning(f"GPU加载失败: {gpu_error}")
                    logger.info("🔄 尝试使用CPU加载ASR模型...")
                    model = AutoModel(
                        model=config.ASR_MODEL,
                        device="cpu",
                        disable_update=True
                    )
                    logger.info("✅ ASR模型使用CPU")
                

                
            else:
                raise ValueError(f"未知模型类型: {model_type}")
            
            self._models[model_type] = model
            
        except Exception as e:
            logger.error(f"❌ 加载 {model_type} 模型失败: {e}")
            raise
    
    def get_model(self, model_type: str):
        """获取模型"""
        model_type = model_type.lower()
        
        with self._model_lock:
            if model_type not in self._models:
                logger.warning(f"模型 {model_type} 未加载")
                return None
            
            return self._models.get(model_type)
    
    def kws_detect(self, audio_data, sample_rate: int = 16000) -> Dict[str, Any]:
        """KWS检测 - 基于您的正确实现，使用临时文件方式"""
        import tempfile
        import soundfile as sf
        import os

        try:
            kws_model = self.get_model("kws")
            if kws_model is None:
                return {"detected": False, "keyword": "", "confidence": 0.0}

            # 转换音频格式为numpy数组
            if isinstance(audio_data, bytes):
                audio_data = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            elif isinstance(audio_data, np.ndarray) and audio_data.dtype == np.int16:
                audio_data = audio_data.astype(np.float32) / 32768.0

            # 确保音频长度足够
            min_samples = sample_rate  # 至少1秒
            if len(audio_data) < min_samples:
                return {"detected": False, "keyword": "", "confidence": 0.0}

            # 创建临时WAV文件 (ModelScope KWS需要文件输入)
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 写入音频文件
                sf.write(temp_path, audio_data, sample_rate)

                # 使用ModelScope pipeline进行检测 - 使用文件路径
                result = kws_model(audio_in=temp_path)

                logger.info(f"KWS原始结果: {result}")
                logger.info(f"KWS结果类型: {type(result)}")
                if isinstance(result, dict):
                    logger.info(f"KWS结果键: {list(result.keys())}")
                    kws_list = result.get("kws_list", [])
                    logger.info(f"KWS列表长度: {len(kws_list)}")
                    if len(kws_list) > 0:
                        logger.info(f"KWS列表内容: {kws_list}")
                elif isinstance(result, list):
                    logger.info(f"KWS结果长度: {len(result)}")
                    if len(result) > 0:
                        logger.info(f"KWS第一个元素: {result[0]}")

                # 解析结果 - 基于真实的KWS输出格式
                if result:
                    detected = False
                    keyword = ""
                    confidence = 0.0

                    # 真实的KWS输出格式: {'kws_type': 'wav', 'wav_count': 1, 'kws_list': [...]}
                    if isinstance(result, dict):
                        kws_list = result.get("kws_list", [])

                        # 检查kws_list中是否有检测到的关键词
                        if isinstance(kws_list, list) and len(kws_list) > 0:
                            for kws_item in kws_list:
                                if isinstance(kws_item, dict):
                                    # 检查关键词字段
                                    keyword_detected = kws_item.get("keyword", "")
                                    text_detected = kws_item.get("text", "")

                                    # 检查是否包含"小云"
                                    if "小云" in keyword_detected or "小云" in text_detected:
                                        detected = True
                                        keyword = "小云"
                                        confidence = kws_item.get("confidence", 0.8)
                                        break
                                elif isinstance(kws_item, str) and "小云" in kws_item:
                                    detected = True
                                    keyword = "小云"
                                    confidence = 0.8
                                    break

                    return {
                        "detected": detected,
                        "keyword": keyword,
                        "confidence": confidence
                    }

                return {"detected": False, "keyword": "", "confidence": 0.0}

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"KWS检测失败: {e}")
            return {"detected": False, "keyword": "", "confidence": 0.0}
    
    def vad_detect(self, audio_data) -> Dict[str, Any]:
        """VAD检测"""
        try:
            vad_model = self.get_model("vad")
            if vad_model is None:
                return {"speech_detected": True}  # 默认有语音
            
            result = vad_model.generate(input=audio_data)
            
            # 解析VAD结果
            if result and len(result) > 0:
                vad_result = result[0]
                speech_detected = len(vad_result.get("value", [])) > 0
                return {"speech_detected": speech_detected}
            
            return {"speech_detected": False}
            
        except Exception as e:
            logger.error(f"VAD检测失败: {e}")
            return {"speech_detected": True}  # 出错时默认有语音
    
    def asr_recognize(self, audio_data) -> Dict[str, Any]:
        """ASR识别"""
        import tempfile
        import soundfile as sf
        import os

        try:
            asr_model = self.get_model("asr")
            if asr_model is None:
                return {"text": "", "confidence": 0.0}

            # 转换音频格式为numpy数组
            if isinstance(audio_data, bytes):
                audio_data = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            elif isinstance(audio_data, np.ndarray) and audio_data.dtype == np.int16:
                audio_data = audio_data.astype(np.float32) / 32768.0

            # 确保音频长度足够
            sample_rate = 16000
            min_samples = sample_rate  # 至少1秒
            if len(audio_data) < min_samples:
                return {"text": "", "confidence": 0.0}

            # 创建临时WAV文件 (ASR也需要文件输入)
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 写入音频文件
                sf.write(temp_path, audio_data, sample_rate)

                # 使用FunASR进行识别
                result = asr_model.generate(input=temp_path)

                logger.debug(f"ASR原始结果: {result}")

                # 解析ASR结果
                if result and len(result) > 0:
                    asr_result = result[0]
                    text = asr_result.get("text", "")
                    return {"text": text, "confidence": 1.0}

                return {"text": "", "confidence": 0.0}

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"ASR识别失败: {e}")
            return {"text": "", "confidence": 0.0}
    


# 创建全局模型管理器实例
model_manager = ModelManager()
