#!/usr/bin/env python3
"""
WebSocket语音客户端
实时录音并传输到服务器
"""

import asyncio
import websockets
import json
import numpy as np
import sounddevice as sd
import base64
import threading
import time
from queue import Queue

class VoiceWebSocketClient:
    def __init__(self, server_url="ws://192.168.28.122:8765"):
        self.server_url = server_url
        self.websocket = None
        self.is_recording = False
        self.audio_queue = Queue()
        self.sample_rate = 16000
        self.chunk_duration = 1.0  # 每1秒发送一次音频
        self.chunk_size = int(self.sample_rate * self.chunk_duration)
        
    def audio_callback(self, indata, frames, time, status):
        """音频回调函数 - 实时录音"""
        if self.is_recording and self.websocket:
            # 转换为float32并放入队列
            audio_chunk = indata[:, 0].astype(np.float32)
            self.audio_queue.put(audio_chunk)
    
    async def send_audio_chunks(self):
        """发送音频块到服务器"""
        audio_buffer = np.array([], dtype=np.float32)
        
        while self.is_recording:
            try:
                # 收集音频数据
                while not self.audio_queue.empty():
                    chunk = self.audio_queue.get_nowait()
                    audio_buffer = np.concatenate([audio_buffer, chunk])
                
                # 当缓冲区足够大时发送
                if len(audio_buffer) >= self.chunk_size:
                    # 取出一个chunk发送
                    send_chunk = audio_buffer[:self.chunk_size]
                    audio_buffer = audio_buffer[self.chunk_size:]
                    
                    # 编码音频数据
                    audio_bytes = send_chunk.tobytes()
                    audio_b64 = base64.b64encode(audio_bytes).decode('utf-8')
                    
                    # 发送到服务器
                    message = {
                        "type": "audio",
                        "audio": audio_b64,
                        "sample_rate": self.sample_rate,
                        "timestamp": time.time()
                    }
                    
                    await self.websocket.send(json.dumps(message))
                    print("📤 发送音频块...")
                
                await asyncio.sleep(0.1)  # 避免CPU占用过高
                
            except Exception as e:
                print(f"❌ 发送音频失败: {e}")
                break
    
    async def receive_messages(self):
        """接收服务器消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_server_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("🔌 与服务器连接断开")
        except Exception as e:
            print(f"❌ 接收消息失败: {e}")
    
    async def handle_server_message(self, data):
        """处理服务器消息"""
        msg_type = data.get("type")
        message = data.get("message", "")
        
        if msg_type == "status":
            print(f"📢 {message}")
        elif msg_type == "wake_detected":
            keyword = data.get("keyword", "")
            confidence = data.get("confidence", 0) * 100
            print(f"🔔 检测到唤醒词: {keyword} (置信度: {confidence:.1f}%)")
            print("💬 我在听，请继续说话...")
        elif msg_type == "speech_recognized":
            text = data.get("text", "")
            confidence = data.get("confidence", 0) * 100
            print(f"🎯 识别结果: \"{text}\" (置信度: {confidence:.1f}%)")
        elif msg_type == "ai_response":
            response = data.get("text", "")
            print(f"🤖 AI回复: {response}")
        elif msg_type == "error":
            print(f"❌ 服务器错误: {message}")
    
    def start_recording(self):
        """开始录音"""
        if self.is_recording:
            print("⚠️ 已经在录音中")
            return
        
        self.is_recording = True
        print("🎤 开始录音...")
        
        # 启动音频流
        self.audio_stream = sd.InputStream(
            callback=self.audio_callback,
            samplerate=self.sample_rate,
            channels=1,
            dtype=np.float32
        )
        self.audio_stream.start()
    
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return
        
        self.is_recording = False
        print("🔇 停止录音")
        
        if hasattr(self, 'audio_stream'):
            self.audio_stream.stop()
            self.audio_stream.close()
    
    async def connect_and_run(self):
        """连接服务器并运行"""
        try:
            print(f"🔗 连接服务器: {self.server_url}")
            
            async with websockets.connect(self.server_url) as websocket:
                self.websocket = websocket
                print("✅ 连接成功")
                
                # 启动接收消息的任务
                receive_task = asyncio.create_task(self.receive_messages())
                
                # 启动发送音频的任务
                send_task = None
                
                print("\n💡 使用说明:")
                print("1. 输入 'start' 开始录音")
                print("2. 输入 'stop' 停止录音") 
                print("3. 输入 'quit' 退出")
                print("4. 先说'小云'唤醒，然后说问题")
                
                # 命令行交互
                while True:
                    try:
                        user_input = await asyncio.get_event_loop().run_in_executor(
                            None, input, "\n🎵 请输入命令 (start/stop/quit): "
                        )
                        
                        user_input = user_input.strip().lower()
                        
                        if user_input == 'quit':
                            break
                        elif user_input == 'start':
                            if not self.is_recording:
                                self.start_recording()
                                send_task = asyncio.create_task(self.send_audio_chunks())
                            else:
                                print("⚠️ 已经在录音中")
                        elif user_input == 'stop':
                            if self.is_recording:
                                self.stop_recording()
                                if send_task:
                                    send_task.cancel()
                            else:
                                print("⚠️ 没有在录音")
                        else:
                            print("❓ 未知命令")
                            
                    except KeyboardInterrupt:
                        break
                
                # 清理
                self.stop_recording()
                if send_task:
                    send_task.cancel()
                receive_task.cancel()
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    def run(self):
        """运行客户端"""
        print("🎤 WebSocket语音客户端")
        print("=" * 40)
        
        try:
            asyncio.run(self.connect_and_run())
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断")
        finally:
            self.stop_recording()
            print("👋 再见!")

if __name__ == "__main__":
    # 检查依赖
    try:
        import sounddevice as sd
        import websockets
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install sounddevice websockets")
        exit(1)
    
    client = VoiceWebSocketClient()
    client.run()